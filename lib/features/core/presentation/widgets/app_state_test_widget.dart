/// App State Test Widget
///
/// A simple test widget to verify that our Riverpod AppState provider is working
library app_state_test_widget;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';

/// Test widget to demonstrate Riverpod AppState provider functionality
class AppStateTestWidget extends ConsumerWidget {
  const AppStateTestWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appStateProvider);
    final userId = ref.watch(userIdProvider);
    final isLoggedIn = ref.watch(isUserLoggedInProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Riverpod Migration Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Riverpod AppState Provider Test',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // App State Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'App State Status:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    appState.when(
                      data: (state) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('✅ State loaded successfully'),
                          Text(
                              'User ID: ${state.userId.isEmpty ? "Not set" : state.userId}'),
                          Text('Is Logged In: ${state.isLoggedIn}'),
                          Text(
                              'Has Complete Profile: ${state.hasCompleteProfile}'),
                        ],
                      ),
                      loading: () => const Text('⏳ Loading app state...'),
                      error: (error, stack) => Text('❌ Error: $error'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Convenience Providers Test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Convenience Providers:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                        'User ID Provider: ${userId.isEmpty ? "Not set" : userId}'),
                    Text('Is Logged In Provider: $isLoggedIn'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Test Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Actions:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            ref
                                .read(appStateProvider.notifier)
                                .setUserId('test_user_123');
                          },
                          child: const Text('Set Test User ID'),
                        ),
                        const SizedBox(width: 10),
                        ElevatedButton(
                          onPressed: () {
                            ref.read(appStateProvider.notifier).clearUserData();
                          },
                          child: const Text('Clear User Data'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        ref.read(appStateProvider.notifier).refreshAppState();
                      },
                      child: const Text('Refresh App State'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Migration Status
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Migration Status:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text('✅ Riverpod dependencies added'),
                    Text('✅ ProviderScope wrapper added'),
                    Text('✅ BaseNotifier created'),
                    Text('✅ AppState provider working'),
                    Text('✅ Code generation working'),
                    Text('⏳ Use cases integration pending'),
                    Text('⏳ Full feature migration pending'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
