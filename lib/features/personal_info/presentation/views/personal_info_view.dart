/// Personal Info View (Riverpod Version)
///
/// Riverpod version of the personal information screen following MVVM pattern
/// Demonstrates migration from GetX to Riverpod for personal info functionality
library personal_info_view_riverpod;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/personal_info/presentation/providers/personal_info_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/helpers/localization_helper.dart';
import 'package:towasl/shared/widgets/bottom_action_bar.dart';
import 'package:towasl/shared/widgets/input_formatters/input_formatters.dart';

/// Personal Info View (Riverpod Version)
///
/// Screen for entering personal information during onboarding
/// Follows MVVM pattern with Riverpod providers instead of GetX
class PersonalInfoView extends ConsumerStatefulWidget {
  /// Whether this view is in editing mode
  final bool isEditing;

  /// Whether this view is in view-only mode (read-only)
  final bool isViewOnly;

  /// User ID for loading personal info (optional, will use provider if not provided)
  final String? userId;

  /// Creates a PersonalInfoView
  const PersonalInfoView({
    super.key,
    this.isEditing = false,
    this.isViewOnly = false,
    this.userId,
  });

  @override
  ConsumerState<PersonalInfoView> createState() => _PersonalInfoViewState();
}

/// State for the PersonalInfoView
class _PersonalInfoViewState extends ConsumerState<PersonalInfoView> {
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Load user data when in view-only mode
    if (widget.isViewOnly) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Use provided userId or fall back to provider
        final userId = widget.userId ?? ref.read(userIdProvider);
        if (kDebugMode) {
          print(
              'PersonalInfoView: isViewOnly=true, userId=$userId (provided: ${widget.userId})');
        }
        if (userId != null && userId.isNotEmpty) {
          if (kDebugMode) {
            print(
                'PersonalInfoView: Loading user personal info for userId: $userId');
          }
          ref.read(personalInfoProvider.notifier).loadUserPersonalInfo(userId);
        } else {
          if (kDebugMode) {
            print(
                'PersonalInfoView: No userId found, cannot load personal info');
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final personalInfoState = ref.watch(personalInfoProvider);
    final isSaving = ref.watch(isPersonalInfoSavingProvider);
    final isFormValid = ref.watch(isPersonalInfoFormValidProvider);
    final localizedGenders =
        LocalizationHelper.getLocalizedGenderOptions(context);
    final localizedNationalities =
        LocalizationHelper.getLocalizedNationalityOptions(context);

    // Update text controller when state changes in view-only mode
    if (widget.isViewOnly && personalInfoState.birthdayYear.isNotEmpty) {
      final controller =
          ref.read(personalInfoProvider.notifier).birthdayYearController;
      if (controller.text != personalInfoState.birthdayYear) {
        controller.text = personalInfoState.birthdayYear;
      }
    }

    // Debug logging for view-only mode
    if (kDebugMode) {
      if (widget.isViewOnly) {
        print('PersonalInfoView build: isViewOnly=true');
        print('  - isLoading: ${personalInfoState.isLoading}');
        print('  - birthdayYear: "${personalInfoState.birthdayYear}"');
        print('  - gender: "${personalInfoState.gender}"');
        print('  - nationality: "${personalInfoState.nationality}"');
        final controller =
            ref.read(personalInfoProvider.notifier).birthdayYearController;
        print('  - controller.text: "${controller.text}"');
      }
    }

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    _buildHeader(),

                    const SizedBox(height: 40),

                    // Form fields
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // Birth year field
                            _buildBirthYearField(),

                            const SizedBox(height: 20),

                            // Gender field
                            _buildGenderField(
                                localizedGenders, personalInfoState.gender),

                            const SizedBox(height: 20),

                            // Nationality field
                            _buildNationalityField(localizedNationalities,
                                personalInfoState.nationality),

                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Loading overlay for saving
            if (isSaving)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),

            // Loading overlay for loading data in view-only mode
            if (widget.isViewOnly && personalInfoState.isLoading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: widget.isViewOnly
          ? null
          : BottomActionBar(
              buttonText: AppLocalizations.of(context).continueText,
              isEnabled: isFormValid,
              isLoading: isSaving,
              onPressed: isFormValid
                  ? () {
                      if (_formKey.currentState?.validate() ?? false) {
                        _showConfirmationDialog();
                      }
                    }
                  : null,
            ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.whiteIvory,
      elevation: 0,
      automaticallyImplyLeading: widget.isViewOnly,
      leading: widget.isViewOnly
          ? IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.greyDark),
              onPressed: () => Navigator.of(context).pop(),
            )
          : null,
      title: Text(
        AppLocalizations.of(context).personalInformation,
        style: const TextStyle(
          color: AppColors.greyDark,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }

  /// Build header section
  Widget _buildHeader() {
    if (widget.isViewOnly) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).personalInformation,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppColors.greyDark,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            AppLocalizations.of(context).personalInfoDescription,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.blackAsh,
              height: 1.5,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).tellUsAboutYourself,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppColors.greyDark,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          AppLocalizations.of(context).personalInfoDescription,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.blackAsh,
            height: 1.5,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            border: Border.all(color: Colors.orange.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Row(
            textDirection: TextDirection.rtl,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 24,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'أدخل بيانات دقيقة لتحسين تجربة التطبيق',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أنت مسؤول عن صحة بياناتك، لن يتم مشاركتها ولن تظهر للغير',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.greyDark,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build birth year field
  Widget _buildBirthYearField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).birthYear,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.greyDark,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller:
              ref.read(personalInfoProvider.notifier).birthdayYearController,
          keyboardType: TextInputType.number,
          enabled: !widget.isViewOnly,
          inputFormatters: widget.isViewOnly
              ? null
              : [
                  const ArabicToEnglishNumeralFormatter(),
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(4),
                ],
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context).enterYourBirthYear,
            border: const OutlineInputBorder(),
            focusedBorder: const OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.primaryPurple, width: 2),
            ),
            filled: true,
            fillColor: widget.isViewOnly
                ? AppColors.greyLight.withOpacity(0.3)
                : AppColors.whitePure,
          ),
          validator: widget.isViewOnly
              ? null
              : (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppLocalizations.of(context)
                        .pleaseEnterYourBirthYear;
                  }
                  final year = int.tryParse(value.trim());
                  if (year == null ||
                      year < DateTime.now().year - 90 ||
                      year > DateTime.now().year - 18) {
                    return AppLocalizations.of(context)
                        .pleaseEnterValidBirthYear;
                  }
                  return null;
                },
          onChanged: widget.isViewOnly
              ? null
              : (value) {
                  ref.read(personalInfoProvider.notifier).clearMessages();
                },
        ),
      ],
    );
  }

  /// Build gender field
  Widget _buildGenderField(
      List<String> localizedGenders, String selectedGender) {
    // Convert storage value to display value for comparison
    final displayGender = selectedGender.isEmpty
        ? null
        : LocalizationHelper.getGenderDisplayName(context, selectedGender);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).gender,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.greyDark,
          ),
        ),
        const SizedBox(height: 12),

        // Radio buttons for gender selection
        Row(
          children: localizedGenders.map((gender) {
            final isSelected = displayGender == gender;

            return Expanded(
              child: Container(
                margin: const EdgeInsets.only(left: 4, right: 4),
                child: InkWell(
                  onTap: widget.isViewOnly
                      ? null
                      : () {
                          // Convert display value to storage value before saving
                          final storageValue =
                              LocalizationHelper.getGenderStorageValue(
                                  context, gender);
                          ref
                              .read(personalInfoProvider.notifier)
                              .updateGender(storageValue);
                        },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isSelected
                            ? AppColors.primaryPurple
                            : AppColors.greyLight,
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      color: AppColors.whitePure,
                    ),
                    child: Row(
                      textDirection: TextDirection.rtl,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Radio button
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primaryPurple
                                  : AppColors.greyMedium,
                              width: 2,
                            ),
                            color: AppColors.whitePure,
                          ),
                          child: isSelected
                              ? Center(
                                  child: Container(
                                    width: 10,
                                    height: 10,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.primaryPurple,
                                    ),
                                  ),
                                )
                              : null,
                        ),

                        const SizedBox(width: 8),

                        // Gender text
                        Text(
                          gender,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? AppColors.primaryPurple
                                : AppColors.greyDark,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Build nationality field
  Widget _buildNationalityField(
      List<String> localizedNationalities, String selectedNationality) {
    // Convert storage value to display value for showing in dropdown
    final displayNationality = selectedNationality.isEmpty
        ? null
        : LocalizationHelper.getNationalityDisplayName(
            context, selectedNationality);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).nationality,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.greyDark,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.greyLight),
            borderRadius: BorderRadius.circular(4),
            color: widget.isViewOnly
                ? AppColors.greyLight.withOpacity(0.3)
                : AppColors.whitePure,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: displayNationality,
              hint: Text(
                AppLocalizations.of(context).selectYourNationality,
                style: const TextStyle(color: AppColors.greyMedium),
              ),
              isExpanded: true,
              items: localizedNationalities.map((nationality) {
                return DropdownMenuItem<String>(
                  value: nationality,
                  child: Text(nationality),
                );
              }).toList(),
              onChanged: widget.isViewOnly
                  ? null
                  : (value) {
                      if (value != null) {
                        // Convert display value to storage value before saving
                        final storageValue =
                            LocalizationHelper.getNationalityStorageValue(
                                context, value);
                        ref
                            .read(personalInfoProvider.notifier)
                            .updateNationality(storageValue);
                      }
                    },
            ),
          ),
        ),
      ],
    );
  }

  /// Show confirmation dialog before saving personal info
  void _showConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.whiteIvory,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              textDirection: TextDirection.rtl,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'لن يمكنك تعديل بياناتك الشخصية هذه من خلال التطبيق ويجب ان تكون صحيحة',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.greyDark,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            // Edit button (highlighted)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: AppColors.whitePure,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'تعديل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Confirm button
            OutlinedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ref.read(personalInfoProvider.notifier).savePersonalInfo();
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.greyDark,
                side: const BorderSide(color: AppColors.greyMedium),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'تأكيد',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          actionsAlignment: MainAxisAlignment.center,
        );
      },
    );
  }
}
