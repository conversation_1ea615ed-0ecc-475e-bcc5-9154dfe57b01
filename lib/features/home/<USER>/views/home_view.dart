/// Home View
///
/// Simple home screen with welcome message and settings access
/// Follows MVVM pattern with Riverpod providers
library home_view;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/profile/presentation/views/profile_view.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/shared/providers/city_validation_provider.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/shared/mixins/analytics_mixin.dart';

/// Home View
///
/// Main home screen with welcome message and navigation to settings
class HomeView extends ConsumerStatefulWidget {
  /// Creates a HomeView
  const HomeView({super.key});

  @override
  ConsumerState<HomeView> createState() => _HomeViewState();
}

/// State for the HomeView
class _HomeViewState extends ConsumerState<HomeView> with AnalyticsMixin {
  bool _isCheckingCity = true;
  bool? _isCitySupported;

  @override
  void initState() {
    super.initState();

    // Track screen view
    trackScreenView(screenName: AnalyticsScreenNames.home);

    // Load user data and then check city support
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserDataAndCheckCity();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: _buildAppBar(),
      body: SafeArea(
        child: _buildContent(),
      ),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.whiteIvory,
      elevation: 0,
      title: Row(
        children: [
          const Icon(
            Icons.favorite,
            color: AppColors.primaryPurple,
            size: 28,
          ),
          const SizedBox(width: 8),
          Text(
            AppLocalizations.of(context).towasl,
            style: const TextStyle(
              color: AppColors.greyDark,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      actions: [
        // Profile
        IconButton(
          icon: const Icon(
            Icons.person_outline,
            color: AppColors.greyDark,
          ),
          onPressed: () {
            // Log button click
            logButtonClick(
              buttonName: 'profile',
              screenName: AnalyticsScreenNames.home,
            );

            // Log navigation
            logNavigation(
              from: AnalyticsScreenNames.home,
              to: AnalyticsScreenNames.profile,
            );

            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const ProfileView(),
              ),
            );
          },
        ),
      ],
    );
  }

  /// Load user data and then check city support
  Future<void> _loadUserDataAndCheckCity() async {
    try {
      // First, ensure app state is loaded
      await ref.read(appStateProvider.notifier).loadAppState();

      // Small delay to ensure app state is loaded
      await Future.delayed(const Duration(milliseconds: 200));

      // Get user ID from app state
      var userId = ref.read(userIdProvider);

      // If still empty, try getting from storage directly
      if (userId.isEmpty) {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();

        // If we found a user ID in storage, update the app state
        if (userId.isNotEmpty) {
          await ref.read(appStateProvider.notifier).setUserId(userId);
        }
      }

      if (userId.isNotEmpty) {
        // Load user data first
        await ref.read(userProvider.notifier).loadUserData(userId);

        // Longer delay to ensure data is loaded
        await Future.delayed(const Duration(milliseconds: 500));

        // Check if user data is actually loaded
        final userState = ref.read(userProvider);
        if (userState.userModel == null) {
          if (kDebugMode) {
            print('HomeView: User model still null, trying to reload...');
          }
          // Try loading again
          await ref.read(userProvider.notifier).loadUserData(userId);
          await Future.delayed(const Duration(milliseconds: 300));
        }

        // Now check city support
        await _checkCitySupport();
      }

      // Set checking complete
      if (mounted) {
        setState(() {
          _isCheckingCity = false;
        });
      }
    } catch (e) {
      // Handle error silently for production
      if (kDebugMode) {
        print('HomeView: Error loading user data - $e');
      }

      // Set checking complete even on error
      if (mounted) {
        setState(() {
          _isCheckingCity = false;
        });
      }
    }
  }

  /// Check if user's city is supported
  Future<void> _checkCitySupport() async {
    try {
      // Get user ID from app state provider, fallback to storage
      var userId = ref.read(userIdProvider);

      if (kDebugMode) {
        print('HomeView: _checkCitySupport called');
        print('HomeView: userId from provider = $userId');
      }

      // If empty, try getting from storage directly
      if (userId.isEmpty) {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();

        if (kDebugMode) {
          print('HomeView: userId from storage = $userId');
        }
      }

      if (userId.isNotEmpty) {
        // Get user data directly from repository
        final userRepository = ref.read(userRepositoryProvider);
        final userModel = await userRepository.getUserById(userId);

        final userLocation = userModel?.userLocation;

        if (kDebugMode) {
          print('HomeView: userModel from repository = $userModel');
          print('HomeView: userModel?.userId = ${userModel?.userId}');
          print('HomeView: userModel?.mobile = ${userModel?.mobile}');
          print('HomeView: userModel?.gender = ${userModel?.gender}');
          print('HomeView: userLocation = $userLocation');
          print('HomeView: userLocation?.city = ${userLocation?.city}');
          print('HomeView: userLocation?.country = ${userLocation?.country}');
          print('HomeView: city = ${userLocation?.city}');
        }

        if (userLocation?.city != null && userLocation!.city!.isNotEmpty) {
          if (kDebugMode) {
            print('HomeView: Validating city: ${userLocation.city}');
          }
          final result = await ref
              .read(cityValidationProvider.notifier)
              .validateCity(userLocation.city!);

          // Store the result in component state
          if (mounted) {
            setState(() {
              _isCitySupported = result.isSupported;
            });
          }

          if (kDebugMode) {
            print(
                'HomeView: City validation completed - supported: ${result.isSupported}');
          }
        } else {
          // No city to validate, assume supported (show welcome)
          if (mounted) {
            setState(() {
              _isCitySupported = true;
            });
          }

          if (kDebugMode) {
            print('HomeView: No city to validate');
          }
        }
      } else {
        if (kDebugMode) {
          print('HomeView: No user ID available');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('HomeView: Error checking city support - $e');
      }
    }
  }

  /// Build main content
  Widget _buildContent() {
    // Show loading while checking city
    if (_isCheckingCity) {
      return _buildLoadingContent();
    }

    // Use stored result instead of provider state
    if (_isCitySupported == false) {
      if (kDebugMode) {
        print('HomeView: Showing unsupported region content');
      }
      return _buildUnsupportedRegionContent();
    }

    // Show normal welcome content
    if (kDebugMode) {
      print('HomeView: Showing welcome content');
    }
    return _buildWelcomeContent();
  }

  /// Build loading content
  Widget _buildLoadingContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryPurple),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loading,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.greyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Build unsupported region content
  Widget _buildUnsupportedRegionContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_off,
              size: 120,
              color: AppColors.greyMedium,
            ),
            const SizedBox(height: 32),
            Text(
              AppLocalizations.of(context).regionNotSupported,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppColors.greyDark,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).regionNotSupportedDescription,
              style: const TextStyle(
                fontSize: 18,
                color: AppColors.greyMedium,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build welcome content
  Widget _buildWelcomeContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.favorite,
              size: 120,
              color: AppColors.primaryPurple,
            ),
            const SizedBox(height: 32),
            Text(
              AppLocalizations.of(context).welcomeToTowasl,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColors.greyDark,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
